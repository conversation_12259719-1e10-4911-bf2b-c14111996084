<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>划词Bar滚动定位测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            /* 创建一个很长的页面来测试滚动 */
            height: 3000px;
        }
        
        .content-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid #E5E7EB;
        }
        
        .test-text {
            line-height: 1.6;
            font-size: 16px;
            color: #333;
            margin-bottom: 15px;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .instructions {
            background: #e8f4fd;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .instructions ol {
            margin-bottom: 0;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .mock-selection-bar {
            position: absolute;
            background: #4285F4;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            font-size: 14px;
            z-index: 1000;
            display: none;
            pointer-events: none;
        }
        
        .scroll-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 2000;
        }
        
        .section-marker {
            position: absolute;
            left: -40px;
            top: 50%;
            transform: translateY(-50%);
            background: #ff6b6b;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .content-section {
            position: relative;
        }
    </style>
</head>
<body>
    <div class="scroll-indicator" id="scrollIndicator">
        滚动位置: <span id="scrollY">0</span>px<br>
        视口高度: <span id="viewportHeight">0</span>px<br>
        文档高度: <span id="documentHeight">0</span>px
    </div>

    <div class="instructions">
        <h3>📋 测试说明</h3>
        <ol>
            <li>选中下面任意一段文字，会出现模拟的划词工具栏</li>
            <li>滚动页面，观察划词工具栏是否能正确跟随选中文本的位置</li>
            <li>在不同的页面位置选中文字，测试定位算法的准确性</li>
            <li>注意观察右上角的滚动位置信息</li>
        </ol>
    </div>

    <div class="content-section">
        <div class="section-marker">区域1</div>
        <h2>第一部分：基础测试文本</h2>
        <p class="test-text">
            这是第一段测试文本。<span class="highlight">请选中这段高亮文字来测试划词工具栏的定位功能。</span>
            当您选中文字后，应该会看到一个蓝色的工具栏出现在选中文本的下方。
        </p>
        <p class="test-text">
            这里有更多的文本内容用于测试。Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
            Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, 
            quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
        </p>
    </div>

    <div class="content-section">
        <div class="section-marker">区域2</div>
        <h2>第二部分：中间位置测试</h2>
        <p class="test-text">
            这是页面中间位置的测试文本。<span class="highlight">选中这段文字测试滚动后的定位准确性。</span>
            滚动页面后再选中文字，工具栏应该能够正确定位到选中文本的相对位置。
        </p>
        <p class="test-text">
            Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. 
            Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
        </p>
    </div>

    <div class="content-section">
        <div class="section-marker">区域3</div>
        <h2>第三部分：下方位置测试</h2>
        <p class="test-text">
            这是页面下方的测试文本。<span class="highlight">在页面底部选中文字时，工具栏应该显示在文本上方。</span>
            这样可以确保工具栏始终在可视区域内。
        </p>
        <p class="test-text">
            Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, 
            totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt.
        </p>
    </div>

    <div class="mock-selection-bar" id="mockSelectionBar">
        📝 模拟划词工具栏
    </div>

    <script>
        const mockBar = document.getElementById('mockSelectionBar');
        const scrollIndicator = document.getElementById('scrollIndicator');
        const scrollYSpan = document.getElementById('scrollY');
        const viewportHeightSpan = document.getElementById('viewportHeight');
        const documentHeightSpan = document.getElementById('documentHeight');

        // 更新滚动指示器
        function updateScrollIndicator() {
            const scrollY = window.pageYOffset || document.documentElement.scrollTop;
            const viewportHeight = window.innerHeight;
            const documentHeight = Math.max(document.documentElement.scrollHeight, document.body.scrollHeight);
            
            scrollYSpan.textContent = Math.round(scrollY);
            viewportHeightSpan.textContent = viewportHeight;
            documentHeightSpan.textContent = documentHeight;
        }

        // 模拟划词工具栏定位逻辑（基于我们修改后的算法）
        function positionMockSelectionBar() {
            const selection = window.getSelection();
            if (!selection || selection.rangeCount === 0) {
                mockBar.style.display = 'none';
                return;
            }

            const range = selection.getRangeAt(0);
            const rect = range.getBoundingClientRect();
            
            // 如果选择区域不可见，隐藏工具栏
            if (rect.width === 0 && rect.height === 0) {
                mockBar.style.display = 'none';
                return;
            }

            const offsetY = 8;
            const barWidth = 200;
            const barHeight = 40;

            // 获取页面滚动偏移量
            const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
            const scrollY = window.pageYOffset || document.documentElement.scrollTop;

            // 将视口坐标转换为文档坐标（相对于文档流的绝对定位）
            let left = rect.left + scrollX;
            let top = rect.bottom + scrollY + offsetY;

            // 获取文档尺寸和视口尺寸
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            const documentWidth = Math.max(document.documentElement.scrollWidth, document.body.scrollWidth);
            const documentHeight = Math.max(document.documentElement.scrollHeight, document.body.scrollHeight);

            // 确保工具栏不会超出视口右边界（考虑当前滚动位置）
            if (left - scrollX + barWidth > viewportWidth) {
                left = rect.right + scrollX - barWidth;
            }

            // 确保工具栏不会超出视口下边界（考虑当前滚动位置）
            if (top - scrollY + barHeight > viewportHeight) {
                top = rect.top + scrollY - barHeight - offsetY;
            }

            // 确保不会超出文档边界
            left = Math.max(5, Math.min(left, documentWidth - barWidth - 5));
            top = Math.max(5, Math.min(top, documentHeight - barHeight - 5));

            mockBar.style.left = `${left}px`;
            mockBar.style.top = `${top}px`;
            mockBar.style.display = 'block';
            
            console.log('Mock bar positioned at:', { left, top, scrollX, scrollY, rect });
        }

        // 处理文本选择
        function handleSelection() {
            setTimeout(() => {
                const selection = window.getSelection();
                if (selection && selection.toString().trim()) {
                    positionMockSelectionBar();
                } else {
                    mockBar.style.display = 'none';
                }
            }, 10);
        }

        // 处理滚动事件
        function handleScroll() {
            updateScrollIndicator();
            // 如果有选中的文本，重新定位工具栏
            const selection = window.getSelection();
            if (selection && selection.toString().trim()) {
                positionMockSelectionBar();
            }
        }

        // 绑定事件监听器
        document.addEventListener('mouseup', handleSelection);
        document.addEventListener('keyup', handleSelection);
        document.addEventListener('selectionchange', handleSelection);
        window.addEventListener('scroll', handleScroll, { passive: true });
        window.addEventListener('resize', () => {
            updateScrollIndicator();
            positionMockSelectionBar();
        });

        // 初始化
        updateScrollIndicator();
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码块显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .code-block {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
        }
        pre {
            background: #f8f8f8;
            border: 1px solid #e1e1e1;
            border-radius: 4px;
            padding: 12px;
            overflow-x: auto;
        }
        code {
            background: #f1f1f1;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.normal {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>代码块显示测试页面</h1>
    
    <div class="test-section">
        <h2>测试1: 普通代码块</h2>
        <pre><code>function hello() {
    console.log("Hello World!");
    return "success";
}</code></pre>
    </div>

    <div class="test-section">
        <h2>测试2: 内联代码</h2>
        <p>这是一个内联代码示例：<code>const x = 42;</code></p>
    </div>

    <div class="test-section">
        <h2>测试3: JavaScript对象</h2>
        <div class="code-block" id="js-object-test">
            <!-- 这里会通过JavaScript动态插入内容 -->
        </div>
    </div>

    <div class="test-section">
        <h2>测试4: 检查Object.prototype</h2>
        <div id="object-prototype-test"></div>
    </div>

    <div class="test-section">
        <h2>测试5: 检查toString方法</h2>
        <div id="tostring-test"></div>
    </div>

    <div class="status" id="status">
        正在检查...
    </div>

    <script>
        // 测试JavaScript对象显示
        function testObjectDisplay() {
            const testObj = {
                name: "test",
                value: 123,
                nested: {
                    prop: "nested value"
                }
            };
            
            const container = document.getElementById('js-object-test');
            container.innerHTML = `
                <p>对象字面量: ${JSON.stringify(testObj, null, 2)}</p>
                <p>对象toString: ${testObj.toString()}</p>
                <p>对象直接显示: ${testObj}</p>
            `;
        }

        // 检查Object.prototype是否被修改
        function checkObjectPrototype() {
            const container = document.getElementById('object-prototype-test');
            const originalToString = Object.prototype.toString;
            
            let result = '<p>Object.prototype.toString: ';
            if (originalToString === Object.prototype.toString) {
                result += '<span style="color: green;">正常</span></p>';
            } else {
                result += '<span style="color: red;">已被修改!</span></p>';
            }
            
            // 检查是否有额外的属性
            const prototypeProps = Object.getOwnPropertyNames(Object.prototype);
            result += `<p>Object.prototype属性数量: ${prototypeProps.length}</p>`;
            result += `<p>属性列表: ${prototypeProps.join(', ')}</p>`;
            
            container.innerHTML = result;
        }

        // 检查toString方法
        function checkToString() {
            const container = document.getElementById('tostring-test');
            const testObj = {};
            
            let result = `<p>空对象toString: "${testObj.toString()}"</p>`;
            result += `<p>空对象直接转换: "${testObj}"</p>`;
            
            // 测试不同类型的toString
            result += `<p>数组toString: "${[1,2,3].toString()}"</p>`;
            result += `<p>数字toString: "${(123).toString()}"</p>`;
            result += `<p>布尔toString: "${true.toString()}"</p>`;
            
            container.innerHTML = result;
        }

        // 运行所有测试
        function runTests() {
            try {
                testObjectDisplay();
                checkObjectPrototype();
                checkToString();
                
                document.getElementById('status').className = 'status normal';
                document.getElementById('status').textContent = '所有测试完成 - 页面正常';
            } catch (error) {
                document.getElementById('status').className = 'status error';
                document.getElementById('status').textContent = `测试出错: ${error.message}`;
            }
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);

        // 监听页面变化，检测是否有外部脚本影响
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    console.log('页面内容发生变化:', mutation);
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    </script>
</body>
</html>

import React from 'react';

interface ImgIconProps {
    src: string;
    alt?: string;
    className?: string;
    width?: number | string;
    height?: number | string;
}

export const ImgIcon: React.FC<ImgIconProps> = ({
    src,
    alt = '',
    className = '',
    width = '16',
    height = '16',
}) => {
    return (
        <img
            src={src}
            alt={alt}
            className={className}
            width={width}
            height={height}
            style={{ objectFit: 'contain' }}
        />
    );
};

export default ImgIcon;

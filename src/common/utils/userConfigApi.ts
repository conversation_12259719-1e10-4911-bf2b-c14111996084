import { buildApi } from './api'
import { userStorage } from './userStorage'
import { getBaseUrl } from './baseUrl'

// 用户配置数据类型定义
export interface UserAssistantConfig {
    userId: string
    floatingDisabledWebsites: string[]
    selectionDisabledWebsites: string[]
    translationDefaultTargetLanguage: string
}

// API响应类型
export interface ApiResponse<T> {
    code: string
    msg: string
    resultData: T
}

// 获取用户配置的请求参数
export interface GetUserConfigRequest {
    userId: string
}

// 保存用户配置的请求参数
export interface SaveUserConfigRequest {
    userId: string
    floatingDisabledWebsites?: string[]
    selectionDisabledWebsites?: string[]
    translationDefaultTargetLanguage?: string
}

// 获取用户配置接口
export const getUserAssistantConfig = buildApi<
    GetUserConfigRequest,
    ApiResponse<UserAssistantConfig>
>('POST', '/web/assistant/userConfig/getUserAssistantConfig', {
    baseUrl: getBaseUrl(),
    transform: (res) => res as ApiResponse<UserAssistantConfig>,
})

// 保存用户配置接口
export const saveUserAssistantConfig = buildApi<
    SaveUserConfigRequest,
    ApiResponse<UserAssistantConfig>
>('POST', '/web/assistant/userConfig/saveUserAssistantConfig', {
    baseUrl: getBaseUrl(),
    transform: (res) => res as ApiResponse<UserAssistantConfig>,
})

// 获取用户ID的工具函数
export const getUserId = async (): Promise<string> => {
    try {
        const userInfo = await userStorage.getUserInfo()
        if (userInfo && userInfo.userId) {
            return userInfo.userId
        }
        // 如果没有userId，尝试使用id字段
        if (userInfo && userInfo.id) {
            return userInfo.id
        }
        // 如果都没有，返回默认值
        return 'anonymous_user'
    } catch (error) {
        console.error('获取用户ID失败:', error)
        return 'anonymous_user'
    }
}

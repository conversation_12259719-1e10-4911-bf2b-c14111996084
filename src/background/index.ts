/**
 * Service Worker：用来监听事件、发送消息
 */
import { EContentsMessageType } from '@src/common/const'
import { EIP_CONFIG } from '@src/common/const'
import { handleFloatingButtonAction } from './components/floatingButton'

// 监听来自内容脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 处理悬浮球的操作
  if (message.type === EContentsMessageType.FloatingButton) {
    handleFloatingButtonAction(message, sender, sendResponse)
    return true
  }
  // 处理划词工具栏的操作
  if (message.action) {
    console.log(
      '收到划词工具栏操作:',
      message.action,
      '选中文本:',
      message.text
    )

    switch (message.action) {
      case 'open-panel':
        // 打开侧边栏面板
        chrome.sidePanel.open({ windowId: sender.tab?.windowId })
        break

      case 'summary':
        // 处理总结操作
        handleSummaryAction(message.text, sender.tab?.id)
        break

      case 'translate':
        // 处理翻译操作
        handleTranslateAction(message.text, sender.tab?.id)
        break

      case 'text-condenser':
        // 处理缩写操作
        handleTextOperation(message.text, '缩写', sender.tab?.id)
        break

      case 'text-expander':
        // 处理扩写操作
        handleTextOperation(message.text, '扩写', sender.tab?.id)
        break

      case 'text-polisher':
        // 处理润色操作
        handleTextOperation(message.text, '润色', sender.tab?.id)
        break

      case 'grammar-corrector':
        // 处理修正拼写和语义操作
        handleTextOperation(message.text, '修正拼写和语义', sender.tab?.id)
        break

      case 'CONTINUE_ASK_TO_BACKGROUND':
        // 处理继续问操作
        handleContinueAsk(
          message.question,
          message.originalText,
          message.originalAction,
          message.conversationId,
          sender.tab?.windowId
        )
        break
    }
  }

  return true // 表示异步响应
})

// 处理总结操作
function handleSummaryAction(text: string, tabId?: number) {
  // 这里可以实现总结功能，例如调用AI API进行总结
  console.log('执行总结操作:', text)

  // 示例：向侧边栏发送消息，请求总结
  chrome.runtime.sendMessage({
    type: 'PROCESS_TEXT',
    operation: '总结',
    text: text,
  })
}

// 处理翻译操作
function handleTranslateAction(text: string, tabId?: number) {
  // 这里可以实现翻译功能，例如调用翻译API
  console.log('执行翻译操作:', text)

  // 示例：向侧边栏发送消息，请求翻译
  chrome.runtime.sendMessage({
    type: 'PROCESS_TEXT',
    operation: '翻译',
    text: text,
  })
}

// 处理文本操作（缩写、扩写、润色、修正等）
function handleTextOperation(text: string, operation: string, tabId?: number) {
  console.log(`执行${operation}操作:`, text)

  // 示例：向侧边栏发送消息，请求处理文本
  chrome.runtime.sendMessage({
    type: 'PROCESS_TEXT',
    operation: operation,
    text: text,
  })
}

// 处理继续问操作
async function handleContinueAsk(
  question: string,
  originalText: string,
  originalAction: string,
  conversationId?: string,
  windowId?: number
) {
  console.log(
    '执行继续问操作:',
    question,
    '原文:',
    originalText,
    '原操作:',
    originalAction,
    'conversationId:',
    conversationId
  )

  // 打开侧边栏面板
  if (windowId) {
    await chrome.sidePanel.open({ windowId })

  }

  setTimeout(() => {
    // 向侧边栏发送消息，传递继续问的内容
    chrome.runtime.sendMessage({
      type: 'CONTINUE_ASK_TO_SIDEBAR',
      question: question,
      originalText: originalText,
      originalAction: originalAction,
      conversationId: conversationId,
    }).catch(error => {
      console.log('Failed to send message to sidebar:', error);
    });
  }, 1500)
}

// 监听登录接口调用
chrome.webRequest.onCompleted.addListener(
  function (details) {
    if (details.url.includes('/gateway/login')) {
      console.log('检测到登录接口调用:', details)
      chrome.runtime.sendMessage({
        type: 'loginSuccess',
        payload: { tabId: details.tabId },
      })
    }
  },
  { urls: [`${EIP_CONFIG.LOGIN_API}*`] }
)

// 监听登出接口调用
chrome.webRequest.onCompleted.addListener(
  function (details) {
    if (details.url.includes('gateway/logout')) {
      console.log('检测到登出接口调用:', details)
      chrome.runtime.sendMessage({
        type: 'logoutSuccess',
        payload: { tabId: details.tabId },
      })
    }
  },
  { urls: [`${EIP_CONFIG.LOGOUT_API}*`] }
)

// Add message listener
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'openSettings') {
    chrome.tabs.create({
      url: `chrome-extension://${chrome.runtime.id}/tabs/settings.html`
    });
  }
});

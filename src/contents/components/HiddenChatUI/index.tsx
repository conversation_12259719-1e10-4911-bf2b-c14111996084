import React, { useRef, Suspense, useEffect, forwardRef, useImperativeHandle, useState, lazy } from 'react'
import { init, log } from '@ht/xlog'
import { isPrd } from '@src/common/utils'
import { MessageType, EFloatButtonActionType } from '@src/common/const'
import { getAllActions } from '@src/common/actions'
import { config } from '@/src/config/aiChatConfig'
import * as styles from './index.module.less'

// 延迟加载ChatUI组件，避免在页面初始化时就加载
const ChatUI = lazy(() => import('@ht/chatui'))

// 保存原始的Object.prototype.toString方法
const originalToString = Object.prototype.toString
const originalValueOf = Object.prototype.valueOf

// 创建一个保护函数来防止全局对象被修改
const protectGlobalObjects = () => {
  // 如果发现toString被修改，恢复原始方法
  if (Object.prototype.toString !== originalToString) {
    console.warn('HiddenChatUI: Object.prototype.toString was modified, restoring original')
    Object.prototype.toString = originalToString
  }

  // 如果发现valueOf被修改，恢复原始方法
  if (Object.prototype.valueOf !== originalValueOf) {
    console.warn('HiddenChatUI: Object.prototype.valueOf was modified, restoring original')
    Object.prototype.valueOf = originalValueOf
  }
}

interface HiddenChatUIProps {
  selectedText?: string;
  textOperation?: string;
}

interface HiddenChatUIRef {
  chatContext: any;
  onSend: (type: string, content: string, options?: any, attachments?: any[]) => Promise<any>;
}


// Content Script环境的消息API适配
const createContentScriptMessageAPI = () => {
  return {
    success: (content: string) => {
      console.log('HiddenChatUI Success:', content)
      // 可以在这里添加页面通知逻辑
    },
    error: (content: string) => {
      console.error('HiddenChatUI Error:', content)
      // 可以在这里添加页面错误通知逻辑
    },
    warning: (content: string) => {
      console.warn('HiddenChatUI Warning:', content)
    },
    info: (content: string) => {
      console.info('HiddenChatUI Info:', content)
    }
  }
}


const HiddenChatUI = forwardRef<HiddenChatUIRef, HiddenChatUIProps>(({ selectedText, textOperation }, ref) => {
  const chatUiRef = useRef<any>(null)
  const actions = getAllActions()
  const [shouldLoadChatUI, setShouldLoadChatUI] = useState(false)

  // 初始化日志
  const initLog = () => {
    init({
      uuid: 'anonymous_user',
      from: 'HtscAiExtension_ContentScript',
      types: ['fetch', 'unhandledrejection', 'windowError'],
      myTrackConfig: {
        product_id: '366',
        product_name: 'Web开发平台',
        channel_env: isPrd ? 'prd_outer' : 'prd_outer_test',
      },
    })
  }

  useEffect(() => {
    initLog()

    // 在组件挂载时保护全局对象
    protectGlobalObjects()

    // 设置定时器定期检查和恢复全局对象
    const protectionInterval = setInterval(protectGlobalObjects, 1000)

    return () => {
      clearInterval(protectionInterval)
    }
  }, [])


  // 日志上报函数
  const onReportLog = (params: any) => {
    const { id, page_id, page_title, btn_id, btn_title } = params
    if (id) {
      log({
        id,
        page_id,
        page_title,
        btn_id,
        btn_title,
      })
    }
  }

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    get chatContext() {
      // 只有在真正需要时才加载ChatUI
      if (!shouldLoadChatUI) {
        console.log('HiddenChatUI: Loading ChatUI on demand')
        setShouldLoadChatUI(true)
      }
      return chatUiRef.current?.chatContext
    },
    onSend: async (type: string, content: string, options?: any, attachments?: any[]) => {
      // 只有在真正需要时才加载ChatUI
      if (!shouldLoadChatUI) {
        console.log('HiddenChatUI: Loading ChatUI for onSend')
        setShouldLoadChatUI(true)
        // 等待一小段时间让ChatUI加载
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      if (chatUiRef.current?.chatContext?.onSend) {
        return await chatUiRef.current.chatContext.onSend(type, content, {
          ...options,
          extendParams: {
            ...options.extendParams,
            hideConversation: true,
          }
        }, attachments)
      }
      throw new Error('ChatUI not initialized')
    }
  }), [shouldLoadChatUI])

  console.log('hidden chat ui render');

  return (
    <div className={styles.hiddenChatUIContainer}>
      {shouldLoadChatUI && (
        <Suspense fallback={<div>Loading...</div>}>
          <ChatUI
            navbar={{
              showLogo: false,
              showCloseButton: false,
              title: '',
            }}
            ref={chatUiRef}
            config={config}
            actions={actions}
            renderWelcome={() => null} // 不渲染欢迎页面
            onReportLog={onReportLog}
            inputOptions={{
              minRows: 2,
            }}
            renderFooterVersion={() => null} // 不渲染版本信息
            showStopAnswer={true}
            showToken={false}
            showHallucination={false}
            historyConversation={{
              navbar: {
                showCloseButton: false,
              }
            }}
            messageContainerConfig={{}}
          />
        </Suspense>
      )}
    </div>
  )
})

HiddenChatUI.displayName = 'HiddenChatUI'

export default HiddenChatUI
export type { HiddenChatUIRef }

import React, { useRef, useEffect, forwardRef, useImperativeHandle } from 'react'
import { init, log } from '@ht/xlog'
import { isPrd } from '@src/common/utils'
import { getAllActions } from '@src/common/actions'
import { config } from '@/src/config/aiChatConfig'
import * as styles from './index.module.less'

interface IsolatedChatUIProps {
  selectedText?: string;
  textOperation?: string;
}

interface IsolatedChatUIRef {
  chatContext: any;
  onSend: (type: string, content: string, options?: any, attachments?: any[]) => Promise<any>;
}

/**
 * 完全隔离的ChatUI组件
 * 使用iframe来完全隔离ChatUI，避免影响宿主页面
 */
const IsolatedChatUI = forwardRef<IsolatedChatUIRef, IsolatedChatUIProps>(({ selectedText, textOperation }, ref) => {
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const chatUIInstanceRef = useRef<any>(null)
  const actions = getAllActions()

  // 初始化日志
  const initLog = () => {
    init({
      uuid: 'anonymous_user',
      from: 'HtscAiExtension_ContentScript_Isolated',
      types: ['fetch', 'unhandledrejection', 'windowError'],
      myTrackConfig: {
        product_id: '366',
        product_name: 'Web开发平台',
        channel_env: isPrd ? 'prd_outer' : 'prd_outer_test',
      },
    })
  }

  // 日志上报函数
  const onReportLog = (params: any) => {
    const { id, page_id, page_title, btn_id, btn_title } = params
    if (id) {
      log({
        id,
        page_id,
        page_title,
        btn_id,
        btn_title,
      })
    }
  }

  useEffect(() => {
    initLog()
    
    // 创建iframe内容
    const createIframeContent = () => {
      if (!iframeRef.current) return

      const iframe = iframeRef.current
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
      
      if (!iframeDoc) return

      // 创建完全隔离的HTML内容
      iframeDoc.open()
      iframeDoc.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>Isolated ChatUI</title>
          <style>
            body {
              margin: 0;
              padding: 0;
              width: 1px;
              height: 1px;
              overflow: hidden;
              visibility: hidden;
            }
            #chatui-container {
              position: absolute;
              top: -9999px;
              left: -9999px;
              width: 1px;
              height: 1px;
              overflow: hidden;
              visibility: hidden;
              opacity: 0;
            }
          </style>
        </head>
        <body>
          <div id="chatui-container"></div>
          <script>
            // 在iframe中初始化ChatUI
            window.initChatUI = function() {
              // 这里会通过postMessage与父窗口通信
              window.parent.postMessage({
                type: 'CHATUI_READY',
                source: 'isolated-chatui'
              }, '*');
            };
            
            // 处理来自父窗口的消息
            window.addEventListener('message', function(event) {
              if (event.data.type === 'CHATUI_SEND') {
                // 处理发送消息的请求
                // 这里需要实际的ChatUI实例来处理
              }
            });
          </script>
        </body>
        </html>
      `)
      iframeDoc.close()
    }

    // 监听iframe加载完成
    const handleIframeLoad = () => {
      createIframeContent()
    }

    if (iframeRef.current) {
      iframeRef.current.addEventListener('load', handleIframeLoad)
    }

    // 监听来自iframe的消息
    const handleMessage = (event: MessageEvent) => {
      if (event.data.source === 'isolated-chatui') {
        if (event.data.type === 'CHATUI_READY') {
          console.log('IsolatedChatUI: ChatUI ready in iframe')
        }
      }
    }

    window.addEventListener('message', handleMessage)

    return () => {
      if (iframeRef.current) {
        iframeRef.current.removeEventListener('load', handleIframeLoad)
      }
      window.removeEventListener('message', handleMessage)
    }
  }, [])

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    get chatContext() {
      // 返回一个代理对象，通过iframe通信
      return {
        onSend: async (type: string, content: string, options?: any, attachments?: any[]) => {
          return new Promise((resolve, reject) => {
            if (!iframeRef.current?.contentWindow) {
              reject(new Error('Iframe not ready'))
              return
            }

            // 通过postMessage发送到iframe
            iframeRef.current.contentWindow.postMessage({
              type: 'CHATUI_SEND',
              payload: { type, content, options, attachments }
            }, '*')

            // 这里应该等待iframe的响应
            setTimeout(() => {
              resolve({ success: true })
            }, 100)
          })
        }
      }
    },
    onSend: async (type: string, content: string, options?: any, attachments?: any[]) => {
      return new Promise((resolve, reject) => {
        if (!iframeRef.current?.contentWindow) {
          reject(new Error('Iframe not ready'))
          return
        }

        // 通过postMessage发送到iframe
        iframeRef.current.contentWindow.postMessage({
          type: 'CHATUI_SEND',
          payload: { type, content, options, attachments }
        }, '*')

        // 这里应该等待iframe的响应
        setTimeout(() => {
          resolve({ success: true })
        }, 100)
      })
    }
  }), [])

  console.log('isolated chat ui render')

  return (
    <div className={styles.isolatedChatUIContainer}>
      <iframe
        ref={iframeRef}
        className={styles.isolatedIframe}
        sandbox="allow-scripts allow-same-origin"
        title="Isolated ChatUI"
      />
    </div>
  )
})

IsolatedChatUI.displayName = 'IsolatedChatUI'

export default IsolatedChatUI
export type { IsolatedChatUIRef }

import React from 'react';
import * as styles from './index.module.less';

interface LoadingProps {
  className?: string;
  size?: 'small' | 'medium' | 'large';
  text?: string;
}

const Loading: React.FC<LoadingProps> = ({ 
  className = '', 
  size = 'medium',
  text = '加载中'
}) => {
  return (
    <div className={`${styles.loadingContainer} ${styles[size]} ${className}`}>
      <div className={styles.loadingContent}>
        <div className={styles.dotsContainer}>
          <span className={styles.dot}></span>
          <span className={styles.dot}></span>
          <span className={styles.dot}></span>
        </div>
        {text && <div className={styles.loadingText}>{text}</div>}
      </div>
    </div>
  );
};

export default Loading;

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  min-height: 60px;
  
  &.small {
    min-height: 40px;
    
    .dotsContainer {
      .dot {
        width: 4px;
        height: 4px;
        margin: 0 2px;
      }
    }
    
    .loadingText {
      font-size: 12px;
      margin-top: 6px;
    }
  }
  
  &.medium {
    min-height: 60px;
    
    .dotsContainer {
      .dot {
        width: 6px;
        height: 6px;
        margin: 0 3px;
      }
    }
    
    .loadingText {
      font-size: 14px;
      margin-top: 8px;
    }
  }
  
  &.large {
    min-height: 80px;
    
    .dotsContainer {
      .dot {
        width: 8px;
        height: 8px;
        margin: 0 4px;
      }
    }
    
    .loadingText {
      font-size: 16px;
      margin-top: 10px;
    }
  }
}

.loadingContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.dotsContainer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.dot {
  background-color: #1890ff;
  border-radius: 50%;
  animation: breathe 1.4s ease-in-out infinite both;
  
  &:nth-child(1) {
    animation-delay: -0.32s;
  }
  
  &:nth-child(2) {
    animation-delay: -0.16s;
  }
  
  &:nth-child(3) {
    animation-delay: 0s;
  }
}

.loadingText {
  color: #666;
  text-align: center;
  font-weight: 400;
  opacity: 0.8;
}

@keyframes breathe {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
